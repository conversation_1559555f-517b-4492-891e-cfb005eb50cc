const fs = require('fs');
const path = require('path');
const { formatResponse } = require('../utils/helpers');

// Helper function to read data from JSON files
const readDataFile = (filename) => {
  try {
    const filePath = path.join(__dirname, '..', 'data', filename);
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading ${filename}:`, error);
    return [];
  }
};

// @desc    Get user orders
// @route   GET /api/orders
// @access  Private
const getOrders = async (req, res, next) => {
  try {
    // Read orders from JSON file
    const orders = readDataFile('orders.json');
    
    res.status(200).json(
      formatResponse(true, 'Orders retrieved successfully', orders)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get order by ID
// @route   GET /api/orders/:id
// @access  Private
const getOrder = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // Read orders from JSON file
    const orders = readDataFile('orders.json');
    
    // Find order by ID
    const order = orders.find(o => o.id === parseInt(id));
    
    if (!order) {
      return res.status(404).json(
        formatResponse(false, 'Order not found')
      );
    }
    
    res.status(200).json(
      formatResponse(true, 'Order retrieved successfully', order)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Create new order
// @route   POST /api/orders
// @access  Private
const createOrder = async (req, res, next) => {
  try {
    // In a real app, we would save the order to the database
    // For mock purposes, we'll just return success with a mock order
    
    const mockOrder = {
      id: 3,
      order_number: `LF${Date.now().toString().slice(-6)}XYZ`,
      user_id: 1,
      status: 'pending',
      payment_status: 'pending',
      payment_method: req.body.payment_method || 'credit_card',
      subtotal: req.body.subtotal || 99.99,
      tax_amount: req.body.tax_amount || 8.00,
      shipping_amount: req.body.shipping_amount || 0.00,
      discount_amount: req.body.discount_amount || 0.00,
      total_amount: req.body.total_amount || 107.99,
      currency: 'USD',
      created_at: new Date().toISOString(),
      items: req.body.items || []
    };
    
    res.status(201).json(
      formatResponse(true, 'Order created successfully', mockOrder)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Track order by order number
// @route   GET /api/orders/track/:orderNumber
// @access  Public
const trackOrder = async (req, res, next) => {
  try {
    const { orderNumber } = req.params;
    
    // Read orders from JSON file
    const orders = readDataFile('orders.json');
    
    // Find order by order number
    const order = orders.find(o => o.order_number === orderNumber);
    
    if (!order) {
      return res.status(404).json(
        formatResponse(false, 'Order not found')
      );
    }
    
    // Return limited tracking information for public access
    const trackingInfo = {
      order_number: order.order_number,
      status: order.status,
      created_at: order.created_at,
      shipped_at: order.shipped_at,
      delivered_at: order.delivered_at
    };
    
    res.status(200).json(
      formatResponse(true, 'Order tracking information retrieved successfully', trackingInfo)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Cancel order
// @route   PUT /api/orders/:id/cancel
// @access  Private
const cancelOrder = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // Read orders from JSON file
    const orders = readDataFile('orders.json');
    
    // Find order by ID
    const order = orders.find(o => o.id === parseInt(id));
    
    if (!order) {
      return res.status(404).json(
        formatResponse(false, 'Order not found')
      );
    }
    
    // Check if order can be cancelled
    if (order.status === 'shipped' || order.status === 'delivered') {
      return res.status(400).json(
        formatResponse(false, 'Order cannot be cancelled after shipping')
      );
    }
    
    // In a real app, we would update the order status in the database
    // For mock purposes, we'll just return success
    
    res.status(200).json(
      formatResponse(true, 'Order cancelled successfully', {
        ...order,
        status: 'cancelled',
        updated_at: new Date().toISOString()
      })
    );
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getOrders,
  getOrder,
  createOrder,
  trackOrder,
  cancelOrder
};
