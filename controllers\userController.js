const fs = require('fs');
const path = require('path');
const { formatResponse } = require('../utils/helpers');

// Helper function to read data from JSON files
const readDataFile = (filename) => {
  try {
    const filePath = path.join(__dirname, '..', 'data', filename);
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading ${filename}:`, error);
    return [];
  }
};

// @desc    Get user profile
// @route   GET /api/users/profile
// @access  Private
const getUserProfile = async (req, res, next) => {
  try {
    // In a real app, we would get the user ID from the authenticated request
    // For mock purposes, we'll return the first user
    const users = readDataFile('users.json');
    
    if (users.length === 0) {
      return res.status(404).json(
        formatResponse(false, 'User not found')
      );
    }

    const user = users[0];
    
    res.status(200).json(
      formatResponse(true, 'User profile retrieved successfully', user)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Update user profile
// @route   PUT /api/users/profile
// @access  Private
const updateUserProfile = async (req, res, next) => {
  try {
    // In a real app, we would update the user in the database
    // For mock purposes, we'll just return success
    
    res.status(200).json(
      formatResponse(true, 'User profile updated successfully', {
        ...req.body,
        id: 1,
        updated_at: new Date().toISOString()
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get user addresses
// @route   GET /api/users/addresses
// @access  Private
const getAddresses = async (req, res, next) => {
  try {
    // Mock addresses
    const addresses = [
      {
        id: 1,
        type: 'shipping',
        first_name: 'John',
        last_name: 'Doe',
        address_line_1: '123 Main St',
        city: 'New York',
        state: 'NY',
        postal_code: '10001',
        country: 'United States',
        is_default: true
      },
      {
        id: 2,
        type: 'billing',
        first_name: 'John',
        last_name: 'Doe',
        address_line_1: '456 Park Ave',
        city: 'New York',
        state: 'NY',
        postal_code: '10022',
        country: 'United States',
        is_default: true
      }
    ];
    
    res.status(200).json(
      formatResponse(true, 'Addresses retrieved successfully', addresses)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Add new address
// @route   POST /api/users/addresses
// @access  Private
const addAddress = async (req, res, next) => {
  try {
    // In a real app, we would save the address to the database
    // For mock purposes, we'll just return success
    
    res.status(201).json(
      formatResponse(true, 'Address added successfully', {
        id: Date.now(),
        ...req.body,
        is_default: req.body.is_default || false,
        created_at: new Date().toISOString()
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Update address
// @route   PUT /api/users/addresses/:id
// @access  Private
const updateAddress = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // In a real app, we would update the address in the database
    // For mock purposes, we'll just return success
    
    res.status(200).json(
      formatResponse(true, 'Address updated successfully', {
        id: parseInt(id),
        ...req.body,
        updated_at: new Date().toISOString()
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Delete address
// @route   DELETE /api/users/addresses/:id
// @access  Private
const deleteAddress = async (req, res, next) => {
  try {
    // In a real app, we would delete the address from the database
    // For mock purposes, we'll just return success
    
    res.status(200).json(
      formatResponse(true, 'Address deleted successfully')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Set default address
// @route   PUT /api/users/addresses/:id/default
// @access  Private
const setDefaultAddress = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // In a real app, we would update the default address in the database
    // For mock purposes, we'll just return success
    
    res.status(200).json(
      formatResponse(true, 'Default address updated successfully', {
        id: parseInt(id),
        is_default: true,
        updated_at: new Date().toISOString()
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get user statistics
// @route   GET /api/users/stats
// @access  Private
const getUserStats = async (req, res, next) => {
  try {
    // Mock user statistics
    const stats = {
      total_orders: 5,
      total_spent: 523.45,
      wishlist_count: 8,
      review_count: 3,
      member_since: '2024-01-15T10:30:00Z'
    };
    
    res.status(200).json(
      formatResponse(true, 'User statistics retrieved successfully', stats)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get user reviews
// @route   GET /api/users/reviews
// @access  Private
const getUserReviews = async (req, res, next) => {
  try {
    // Mock user reviews
    const reviews = [
      {
        id: 1,
        product_id: 1,
        product_name: 'Classic White T-Shirt',
        product_image: '/images/products/tshirt-white.jpg',
        rating: 5,
        title: 'Great product!',
        comment: 'I love this product, it\'s exactly what I was looking for.',
        created_at: '2025-05-15T10:30:00Z'
      },
      {
        id: 2,
        product_id: 4,
        product_name: 'Leather Jacket',
        product_image: '/images/products/jacket-leather.jpg',
        rating: 4,
        title: 'Good quality',
        comment: 'Good quality and fast shipping. Would buy again.',
        created_at: '2025-05-10T14:45:00Z'
      }
    ];
    
    res.status(200).json(
      formatResponse(true, 'User reviews retrieved successfully', reviews)
    );
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getUserProfile,
  updateUserProfile,
  getAddresses,
  addAddress,
  updateAddress,
  deleteAddress,
  setDefaultAddress,
  getUserStats,
  getUserReviews
};
