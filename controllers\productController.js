const fs = require('fs');
const path = require('path');
const { formatResponse, calculatePagination } = require('../utils/helpers');

// Helper function to read data from JSON files
const readDataFile = (filename) => {
  try {
    const filePath = path.join(__dirname, '..', 'data', filename);
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading ${filename}:`, error);
    return [];
  }
};

// @desc    Get all products with filtering and pagination
// @route   GET /api/products
// @access  Public
const getProducts = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 12,
      category,
      gender,
      min_price,
      max_price,
      sort = 'created_at',
      order = 'DESC',
      search,
      featured
    } = req.query;

    // Read products from JSON file
    let products = readDataFile('products.json');
    
    // Filter products based on query parameters
    if (category) {
      products = products.filter(p => p.category_slug === category);
    }

    if (gender) {
      products = products.filter(p => p.gender === gender || p.gender === 'unisex');
    }

    if (min_price) {
      products = products.filter(p => (p.sale_price || p.price) >= parseFloat(min_price));
    }

    if (max_price) {
      products = products.filter(p => (p.sale_price || p.price) <= parseFloat(max_price));
    }

    if (featured === 'true') {
      products = products.filter(p => p.is_featured);
    }

    if (search) {
      const searchLower = search.toLowerCase();
      products = products.filter(p => 
        p.name.toLowerCase().includes(searchLower) || 
        (p.short_description && p.short_description.toLowerCase().includes(searchLower))
      );
    }

    // Sort products
    const sortField = ['name', 'price', 'created_at'].includes(sort) ? sort : 'created_at';
    const sortOrder = order.toUpperCase() === 'ASC' ? 1 : -1;
    
    products.sort((a, b) => {
      if (sortField === 'price') {
        const aPrice = a.sale_price || a.price;
        const bPrice = b.sale_price || b.price;
        return sortOrder * (aPrice - bPrice);
      } else if (sortField === 'name') {
        return sortOrder * a.name.localeCompare(b.name);
      } else {
        // Default sort by id (as proxy for created_at)
        return sortOrder * (a.id - b.id);
      }
    });

    // Calculate pagination
    const totalCount = products.length;
    const pagination = calculatePagination(page, limit, totalCount);
    
    // Apply pagination
    const paginatedProducts = products.slice(
      pagination.offset, 
      pagination.offset + pagination.itemsPerPage
    );

    res.status(200).json(
      formatResponse(true, 'Products retrieved successfully', paginatedProducts, pagination)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get single product by ID or slug
// @route   GET /api/products/:identifier
// @access  Public
const getProduct = async (req, res, next) => {
  try {
    const { identifier } = req.params;
    
    // Read products from JSON file
    const products = readDataFile('products.json');
    
    // Check if identifier is numeric (ID) or string (slug)
    const isId = /^\d+$/.test(identifier);
    
    // Find product by ID or slug
    const product = isId 
      ? products.find(p => p.id === parseInt(identifier))
      : products.find(p => p.slug === identifier);

    if (!product) {
      return res.status(404).json(
        formatResponse(false, 'Product not found')
      );
    }

    // Get mock product images
    const images = [
      { 
        image_url: product.primary_image,
        alt_text: product.name,
        is_primary: true
      },
      {
        image_url: product.primary_image.replace('.jpg', '-2.jpg'),
        alt_text: `${product.name} - View 2`,
        is_primary: false
      },
      {
        image_url: product.primary_image.replace('.jpg', '-3.jpg'),
        alt_text: `${product.name} - View 3`,
        is_primary: false
      }
    ];

    // Get mock reviews
    const reviews = [
      {
        id: 1,
        rating: 5,
        title: "Great product!",
        comment: "I love this product, it's exactly what I was looking for.",
        reviewer_name: "John D.",
        created_at: "2025-05-15T10:30:00Z"
      },
      {
        id: 2,
        rating: 4,
        title: "Good quality",
        comment: "Good quality and fast shipping. Would buy again.",
        reviewer_name: "Sarah M.",
        created_at: "2025-05-10T14:45:00Z"
      }
    ];

    // Add images and reviews to product
    const productWithDetails = {
      ...product,
      images,
      reviews
    };

    res.status(200).json(
      formatResponse(true, 'Product retrieved successfully', productWithDetails)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get products by category
// @route   GET /api/products/category/:slug
// @access  Public
const getProductsByCategory = async (req, res, next) => {
  try {
    const { slug } = req.params;
    const { page = 1, limit = 12, sort = 'created_at', order = 'DESC' } = req.query;

    // Read categories and products from JSON files
    const categories = readDataFile('categories.json');
    const products = readDataFile('products.json');
    
    // Find category by slug
    const category = categories.find(c => c.slug === slug);

    if (!category) {
      return res.status(404).json(
        formatResponse(false, 'Category not found')
      );
    }

    // Filter products by category
    const categoryProducts = products.filter(p => p.category_slug === slug);
    
    // Sort products
    const validSortFields = ['name', 'price', 'created_at'];
    const sortField = validSortFields.includes(sort) ? sort : 'created_at';
    const sortOrder = order.toUpperCase() === 'ASC' ? 1 : -1;
    
    categoryProducts.sort((a, b) => {
      if (sortField === 'price') {
        const aPrice = a.sale_price || a.price;
        const bPrice = b.sale_price || b.price;
        return sortOrder * (aPrice - bPrice);
      } else if (sortField === 'name') {
        return sortOrder * a.name.localeCompare(b.name);
      } else {
        // Default sort by id (as proxy for created_at)
        return sortOrder * (a.id - b.id);
      }
    });

    // Calculate pagination
    const totalCount = categoryProducts.length;
    const pagination = calculatePagination(page, limit, totalCount);
    
    // Apply pagination
    const paginatedProducts = categoryProducts.slice(
      pagination.offset, 
      pagination.offset + pagination.itemsPerPage
    );

    res.status(200).json(
      formatResponse(true, 'Products retrieved successfully', {
        category,
        products: paginatedProducts
      }, pagination)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Search products
// @route   GET /api/products/search
// @access  Public
const searchProducts = async (req, res, next) => {
  try {
    const { q, page = 1, limit = 12 } = req.query;

    if (!q || q.trim().length < 2) {
      return res.status(400).json(
        formatResponse(false, 'Search query must be at least 2 characters long')
      );
    }

    const searchTerm = q.trim().toLowerCase();
    
    // Read products from JSON file
    const products = readDataFile('products.json');
    
    // Search products
    const searchResults = products.filter(product => 
      product.name.toLowerCase().includes(searchTerm) ||
      (product.short_description && product.short_description.toLowerCase().includes(searchTerm)) ||
      (product.category_name && product.category_name.toLowerCase().includes(searchTerm))
    );

    // Calculate pagination
    const totalCount = searchResults.length;
    const pagination = calculatePagination(page, limit, totalCount);
    
    // Apply pagination
    const paginatedResults = searchResults.slice(
      pagination.offset, 
      pagination.offset + pagination.itemsPerPage
    );

    res.status(200).json(
      formatResponse(true, `Found ${totalCount} products for "${q}"`, paginatedResults, pagination)
    );
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getProducts,
  getProduct,
  getProductsByCategory,
  searchProducts
};
