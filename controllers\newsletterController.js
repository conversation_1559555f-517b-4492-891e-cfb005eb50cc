const { executeQuery } = require('../config/database');
const { formatResponse, generateRandomString } = require('../utils/helpers');

// @desc    Subscribe to newsletter
// @route   POST /api/newsletter/subscribe
// @access  Public
const subscribe = async (req, res, next) => {
  try {
    const { email, first_name } = req.body;

    // Check if email already exists
    const existingSubscriptions = await executeQuery(
      'SELECT id, is_active FROM newsletter_subscriptions WHERE email = ?',
      [email]
    );

    if (existingSubscriptions.length > 0) {
      const subscription = existingSubscriptions[0];
      
      if (subscription.is_active) {
        return res.status(400).json(
          formatResponse(false, 'Email is already subscribed to newsletter')
        );
      } else {
        // Reactivate subscription
        await executeQuery(
          'UPDATE newsletter_subscriptions SET is_active = TRUE, first_name = ?, subscribed_at = NOW(), unsubscribed_at = NULL WHERE id = ?',
          [first_name || null, subscription.id]
        );

        return res.status(200).json(
          formatResponse(true, 'Newsletter subscription reactivated successfully')
        );
      }
    }

    // Generate unsubscribe token
    const unsubscribeToken = generateRandomString(32);

    // Create new subscription
    await executeQuery(
      'INSERT INTO newsletter_subscriptions (email, first_name, unsubscribe_token) VALUES (?, ?, ?)',
      [email, first_name || null, unsubscribeToken]
    );

    res.status(201).json(
      formatResponse(true, 'Successfully subscribed to newsletter')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Unsubscribe from newsletter
// @route   POST /api/newsletter/unsubscribe
// @access  Public
const unsubscribe = async (req, res, next) => {
  try {
    const { email, token } = req.body;

    if (!email && !token) {
      return res.status(400).json(
        formatResponse(false, 'Email or unsubscribe token is required')
      );
    }

    let query, params;

    if (token) {
      query = 'SELECT id, email, is_active FROM newsletter_subscriptions WHERE unsubscribe_token = ?';
      params = [token];
    } else {
      query = 'SELECT id, email, is_active FROM newsletter_subscriptions WHERE email = ?';
      params = [email];
    }

    const subscriptions = await executeQuery(query, params);

    if (subscriptions.length === 0) {
      return res.status(404).json(
        formatResponse(false, 'Subscription not found')
      );
    }

    const subscription = subscriptions[0];

    if (!subscription.is_active) {
      return res.status(400).json(
        formatResponse(false, 'Email is already unsubscribed')
      );
    }

    // Update subscription status
    await executeQuery(
      'UPDATE newsletter_subscriptions SET is_active = FALSE, unsubscribed_at = NOW() WHERE id = ?',
      [subscription.id]
    );

    res.status(200).json(
      formatResponse(true, 'Successfully unsubscribed from newsletter')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Check subscription status
// @route   GET /api/newsletter/status/:email
// @access  Public
const getSubscriptionStatus = async (req, res, next) => {
  try {
    const { email } = req.params;

    const subscriptions = await executeQuery(
      'SELECT is_active, subscribed_at, unsubscribed_at FROM newsletter_subscriptions WHERE email = ?',
      [email]
    );

    if (subscriptions.length === 0) {
      return res.status(200).json(
        formatResponse(true, 'Subscription status retrieved', {
          subscribed: false,
          email
        })
      );
    }

    const subscription = subscriptions[0];

    res.status(200).json(
      formatResponse(true, 'Subscription status retrieved', {
        subscribed: subscription.is_active,
        email,
        subscribed_at: subscription.subscribed_at,
        unsubscribed_at: subscription.unsubscribed_at
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get newsletter statistics (Admin only)
// @route   GET /api/newsletter/stats
// @access  Private (Admin)
const getNewsletterStats = async (req, res, next) => {
  try {
    // Get subscription statistics
    const stats = await executeQuery(
      `SELECT 
        COUNT(*) as total_subscriptions,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_subscriptions,
        SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive_subscriptions,
        COUNT(CASE WHEN DATE(subscribed_at) = CURDATE() THEN 1 END) as today_subscriptions,
        COUNT(CASE WHEN WEEK(subscribed_at) = WEEK(NOW()) AND YEAR(subscribed_at) = YEAR(NOW()) THEN 1 END) as this_week_subscriptions,
        COUNT(CASE WHEN MONTH(subscribed_at) = MONTH(NOW()) AND YEAR(subscribed_at) = YEAR(NOW()) THEN 1 END) as this_month_subscriptions
       FROM newsletter_subscriptions`
    );

    // Get recent subscriptions
    const recentSubscriptions = await executeQuery(
      `SELECT email, first_name, subscribed_at, is_active 
       FROM newsletter_subscriptions 
       ORDER BY subscribed_at DESC 
       LIMIT 10`
    );

    // Get subscription trends (last 30 days)
    const trends = await executeQuery(
      `SELECT 
        DATE(subscribed_at) as date,
        COUNT(*) as subscriptions
       FROM newsletter_subscriptions 
       WHERE subscribed_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
       GROUP BY DATE(subscribed_at)
       ORDER BY date DESC`
    );

    res.status(200).json(
      formatResponse(true, 'Newsletter statistics retrieved', {
        stats: stats[0],
        recent_subscriptions: recentSubscriptions,
        trends
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get all subscribers (Admin only)
// @route   GET /api/newsletter/subscribers
// @access  Private (Admin)
const getSubscribers = async (req, res, next) => {
  try {
    const { page = 1, limit = 50, status = 'all' } = req.query;

    let whereClause = '';
    let queryParams = [];

    if (status === 'active') {
      whereClause = 'WHERE is_active = 1';
    } else if (status === 'inactive') {
      whereClause = 'WHERE is_active = 0';
    }

    // Get total count
    const countResult = await executeQuery(
      `SELECT COUNT(*) as total FROM newsletter_subscriptions ${whereClause}`,
      queryParams
    );
    const totalCount = countResult[0].total;

    // Calculate pagination
    const offset = (page - 1) * limit;

    // Get subscribers
    const subscribers = await executeQuery(
      `SELECT email, first_name, is_active, subscribed_at, unsubscribed_at 
       FROM newsletter_subscriptions 
       ${whereClause}
       ORDER BY subscribed_at DESC
       LIMIT ? OFFSET ?`,
      [...queryParams, parseInt(limit), offset]
    );

    res.status(200).json(
      formatResponse(true, 'Subscribers retrieved successfully', subscribers, {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalCount / limit),
        totalCount,
        hasNextPage: page * limit < totalCount,
        hasPrevPage: page > 1
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Export subscribers (Admin only)
// @route   GET /api/newsletter/export
// @access  Private (Admin)
const exportSubscribers = async (req, res, next) => {
  try {
    const { status = 'active' } = req.query;

    let whereClause = '';
    if (status === 'active') {
      whereClause = 'WHERE is_active = 1';
    } else if (status === 'inactive') {
      whereClause = 'WHERE is_active = 0';
    }

    const subscribers = await executeQuery(
      `SELECT email, first_name, is_active, subscribed_at, unsubscribed_at 
       FROM newsletter_subscriptions 
       ${whereClause}
       ORDER BY subscribed_at DESC`
    );

    // Convert to CSV format
    const csvHeader = 'Email,First Name,Status,Subscribed At,Unsubscribed At\n';
    const csvData = subscribers.map(sub => 
      `${sub.email},"${sub.first_name || ''}",${sub.is_active ? 'Active' : 'Inactive'},${sub.subscribed_at || ''},${sub.unsubscribed_at || ''}`
    ).join('\n');

    const csv = csvHeader + csvData;

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename=newsletter-subscribers-${status}-${new Date().toISOString().split('T')[0]}.csv`);
    res.status(200).send(csv);
  } catch (error) {
    next(error);
  }
};

module.exports = {
  subscribe,
  unsubscribe,
  getSubscriptionStatus,
  getNewsletterStats,
  getSubscribers,
  exportSubscribers
};
