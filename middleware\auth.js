const jwt = require('jsonwebtoken');
const { formatResponse } = require('../utils/helpers');

// Mock authentication middleware
const protect = (req, res, next) => {
  try {
    // Get token from header
    const token = req.headers.authorization && 
                 req.headers.authorization.startsWith('Bearer') ? 
                 req.headers.authorization.split(' ')[1] : null;

    if (!token) {
      return res.status(401).json(
        formatResponse(false, 'Not authorized, no token')
      );
    }

    // In a real app, we would verify the token
    // For mock purposes, we'll just set a mock user ID
    req.user = { id: 1 };
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(401).json(
      formatResponse(false, 'Not authorized, token failed')
    );
  }
};

module.exports = { protect };
