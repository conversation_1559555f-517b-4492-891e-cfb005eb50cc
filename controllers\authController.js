const fs = require('fs');
const path = require('path');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { formatResponse } = require('../utils/helpers');

// Helper function to read data from JSON files
const readDataFile = (filename) => {
  try {
    const filePath = path.join(__dirname, '..', 'data', filename);
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading ${filename}:`, error);
    return [];
  }
};

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
const login = async (req, res, next) => {
  try {
    const { email, password } = req.body;
    
    // For mock purposes, we'll accept any login with valid format
    if (!email || !password) {
      return res.status(400).json(
        formatResponse(false, 'Please provide email and password')
      );
    }
    
    // Generate a mock token
    const token = jwt.sign(
      { id: 1 },
      process.env.JWT_SECRET || 'mock-secret-key',
      { expiresIn: '7d' }
    );
    
    // Get first user from mock data
    const users = readDataFile('users.json');
    const user = users.length > 0 ? users[0] : {
      id: 1,
      email,
      first_name: "John",
      last_name: "Doe"
    };
    
    res.status(200).json(
      formatResponse(true, 'Login successful', {
        token,
        user: {
          id: user.id,
          email: user.email,
          first_name: user.first_name,
          last_name: user.last_name
        }
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Register user
// @route   POST /api/auth/register
// @access  Public
const register = async (req, res, next) => {
  try {
    const { email, password, first_name, last_name } = req.body;
    
    // For mock purposes, we'll accept any registration with valid format
    if (!email || !password || !first_name || !last_name) {
      return res.status(400).json(
        formatResponse(false, 'Please provide all required fields')
      );
    }
    
    // Generate a mock token
    const token = jwt.sign(
      { id: 999 },
      process.env.JWT_SECRET || 'mock-secret-key',
      { expiresIn: '7d' }
    );
    
    res.status(201).json(
      formatResponse(true, 'Registration successful', {
        token,
        user: {
          id: 999,
          email,
          first_name,
          last_name
        }
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get current user
// @route   GET /api/auth/me
// @access  Private
const getMe = async (req, res, next) => {
  try {
    // Get first user from mock data
    const users = readDataFile('users.json');
    const user = users.length > 0 ? users[0] : {
      id: 1,
      email: "<EMAIL>",
      first_name: "John",
      last_name: "Doe"
    };
    
    res.status(200).json(
      formatResponse(true, 'User retrieved successfully', user)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Update user profile
// @route   PUT /api/auth/profile
// @access  Private
const updateProfile = async (req, res, next) => {
  try {
    // In a real app, we would update the user in the database
    // For mock purposes, we'll just return success
    
    res.status(200).json(
      formatResponse(true, 'Profile updated successfully', {
        ...req.body,
        id: 1,
        updated_at: new Date().toISOString()
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Change password
// @route   PUT /api/auth/change-password
// @access  Private
const changePassword = async (req, res, next) => {
  try {
    // In a real app, we would update the password in the database
    // For mock purposes, we'll just return success
    
    res.status(200).json(
      formatResponse(true, 'Password changed successfully')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Logout user
// @route   POST /api/auth/logout
// @access  Private
const logout = async (req, res, next) => {
  try {
    // In a real app, we might invalidate the token
    // For mock purposes, we'll just return success
    
    res.status(200).json(
      formatResponse(true, 'Logged out successfully')
    );
  } catch (error) {
    next(error);
  }
};

module.exports = {
  login,
  register,
  getMe,
  updateProfile,
  changePassword,
  logout
};
