const fs = require('fs');
const path = require('path');
const { formatResponse } = require('../utils/helpers');

// Helper function to read data from JSON files
const readDataFile = (filename) => {
  try {
    const filePath = path.join(__dirname, '..', 'data', filename);
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading ${filename}:`, error);
    return [];
  }
};

// @desc    Get cart items
// @route   GET /api/cart
// @access  Private
const getCart = async (req, res, next) => {
  try {
    // In a real app, we would get cart items from the database
    // For mock purposes, we'll return empty cart or sample items
    
    // Sample cart items
    const cartItems = [
      {
        id: 1,
        product_id: 1,
        quantity: 2,
        selected_color: "white",
        selected_size: "M",
        price: 24.99,
        product: {
          name: "Classic White T-Shirt",
          slug: "classic-white-t-shirt",
          primary_image: "/images/products/tshirt-white.jpg"
        }
      }
    ];
    
    res.status(200).json(
      formatResponse(true, 'Cart items retrieved successfully', cartItems)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Add item to cart
// @route   POST /api/cart
// @access  Private
const addToCart = async (req, res, next) => {
  try {
    const { product_id, quantity, selected_color, selected_size } = req.body;
    
    // Read products from JSON file to get product details
    const products = readDataFile('products.json');
    const product = products.find(p => p.id === parseInt(product_id));
    
    if (!product) {
      return res.status(404).json(
        formatResponse(false, 'Product not found')
      );
    }
    
    // Create mock cart item
    const cartItem = {
      id: Date.now(),
      product_id: parseInt(product_id),
      quantity: parseInt(quantity) || 1,
      selected_color,
      selected_size,
      price: product.sale_price || product.price,
      product: {
        name: product.name,
        slug: product.slug,
        primary_image: product.primary_image
      }
    };
    
    res.status(201).json(
      formatResponse(true, 'Item added to cart successfully', cartItem)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Update cart item
// @route   PUT /api/cart/:id
// @access  Private
const updateCartItem = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { quantity } = req.body;
    
    // In a real app, we would update the cart item in the database
    // For mock purposes, we'll just return success
    
    res.status(200).json(
      formatResponse(true, 'Cart item updated successfully', {
        id: parseInt(id),
        quantity: parseInt(quantity)
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Remove cart item
// @route   DELETE /api/cart/:id
// @access  Private
const removeFromCart = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // In a real app, we would remove the cart item from the database
    // For mock purposes, we'll just return success
    
    res.status(200).json(
      formatResponse(true, 'Cart item removed successfully')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Clear cart
// @route   DELETE /api/cart
// @access  Private
const clearCart = async (req, res, next) => {
  try {
    // In a real app, we would clear the cart in the database
    // For mock purposes, we'll just return success
    
    res.status(200).json(
      formatResponse(true, 'Cart cleared successfully')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Merge guest cart with user cart
// @route   POST /api/cart/merge
// @access  Private
const mergeCart = async (req, res, next) => {
  try {
    // In a real app, we would merge the guest cart with the user cart
    // For mock purposes, we'll just return success
    
    res.status(200).json(
      formatResponse(true, 'Cart merged successfully')
    );
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getCart,
  addToCart,
  updateCartItem,
  removeFromCart,
  clearCart,
  mergeCart
};
