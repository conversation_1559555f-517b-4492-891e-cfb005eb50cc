const { executeQuery } = require('../config/database');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const seedData = async () => {
  try {
    console.log('🌱 Starting database seeding...');

    // Clear existing data (in reverse order of dependencies)
    await executeQuery('DELETE FROM order_items');
    await executeQuery('DELETE FROM orders');
    await executeQuery('DELETE FROM cart');
    await executeQuery('DELETE FROM wishlist');
    await executeQuery('DELETE FROM reviews');
    await executeQuery('DELETE FROM product_images');
    await executeQuery('DELETE FROM products');
    await executeQuery('DELETE FROM categories');
    await executeQuery('DELETE FROM user_addresses');
    await executeQuery('DELETE FROM users');
    await executeQuery('DELETE FROM newsletter_subscriptions');

    // Reset auto increment
    await executeQuery('ALTER TABLE users AUTO_INCREMENT = 1');
    await executeQuery('ALTER TABLE categories AUTO_INCREMENT = 1');
    await executeQuery('ALTER TABLE products AUTO_INCREMENT = 1');

    console.log('🗑️  Cleared existing data');

    // Seed Categories
    const categories = [
      { name: 'Dresses', slug: 'dresses', description: 'Elegant and stylish dresses for every occasion', gender: 'women' },
      { name: 'Tops', slug: 'tops', description: 'Casual and chic tops to complete your look', gender: 'women' },
      { name: 'Bottoms', slug: 'bottoms', description: 'Comfortable and fashionable pants and skirts', gender: 'women' },
      { name: 'Shoes', slug: 'shoes', description: 'Step out in style with our shoe collection', gender: 'unisex' },
      { name: 'Bags', slug: 'bags', description: 'Handbags and purses for the modern woman', gender: 'women' },
      { name: 'Shirts', slug: 'shirts', description: 'Classic and modern shirts for men', gender: 'men' },
      { name: 'Pants', slug: 'pants', description: 'Quality pants and jeans for every style', gender: 'men' },
      { name: 'Jackets', slug: 'jackets', description: 'Stylish outerwear for all seasons', gender: 'unisex' },
      { name: 'Jewelry', slug: 'jewelry', description: 'Beautiful jewelry to complement your style', gender: 'unisex' },
      { name: 'Watches', slug: 'watches', description: 'Luxury timepieces for the discerning individual', gender: 'unisex' },
      { name: 'Sunglasses', slug: 'sunglasses', description: 'Designer frames to protect and style', gender: 'unisex' },
      { name: 'Activewear', slug: 'activewear', description: 'Performance wear for your active lifestyle', gender: 'unisex' },
      { name: 'Formal', slug: 'formal', description: 'Elegant attire for special occasions', gender: 'unisex' }
    ];

    for (const category of categories) {
      await executeQuery(
        'INSERT INTO categories (name, slug, description, gender) VALUES (?, ?, ?, ?)',
        [category.name, category.slug, category.description, category.gender]
      );
    }

    console.log('✅ Categories seeded');

    // Seed Users
    const hashedPassword = await bcrypt.hash('password123', 12);
    
    const users = [
      {
        email: '<EMAIL>',
        password: hashedPassword,
        first_name: 'John',
        last_name: 'Doe',
        phone: '+1234567890',
        is_verified: true
      },
      {
        email: '<EMAIL>',
        password: hashedPassword,
        first_name: 'Jane',
        last_name: 'Smith',
        phone: '+1234567891',
        is_verified: true
      },
      {
        email: '<EMAIL>',
        password: hashedPassword,
        first_name: 'Admin',
        last_name: 'User',
        phone: '+1234567892',
        is_verified: true
      }
    ];

    for (const user of users) {
      await executeQuery(
        'INSERT INTO users (email, password, first_name, last_name, phone, is_verified) VALUES (?, ?, ?, ?, ?, ?)',
        [user.email, user.password, user.first_name, user.last_name, user.phone, user.is_verified]
      );
    }

    console.log('✅ Users seeded');

    // Seed Products
    const products = [
      // Dresses
      {
        name: 'Elegant Summer Dress',
        slug: 'elegant-summer-dress',
        description: 'A beautiful flowing maxi dress perfect for summer occasions. Made from lightweight, breathable fabric.',
        short_description: 'Flowing maxi dress perfect for summer occasions',
        sku: 'DRE-ELESUM-001',
        price: 89.99,
        sale_price: null,
        category_id: 1,
        brand: 'Luxe Fashion',
        colors: JSON.stringify(['Blue', 'Red', 'Green']),
        sizes: JSON.stringify(['XS', 'S', 'M', 'L', 'XL']),
        stock_quantity: 50,
        is_featured: true,
        badge: 'New'
      },
      {
        name: 'Floral Maxi Dress',
        slug: 'floral-maxi-dress',
        description: 'Stunning floral print maxi dress with adjustable straps and flowing silhouette.',
        short_description: 'Stunning floral print maxi dress',
        sku: 'DRE-FLOMAX-002',
        price: 129.99,
        sale_price: null,
        category_id: 1,
        brand: 'Luxe Fashion',
        colors: JSON.stringify(['Floral Print', 'Black Floral']),
        sizes: JSON.stringify(['XS', 'S', 'M', 'L', 'XL']),
        stock_quantity: 30,
        is_featured: true,
        badge: 'Popular'
      },
      // Men's Shirts
      {
        name: 'Premium Cotton Shirt',
        slug: 'premium-cotton-shirt',
        description: 'Classic fit shirt made from premium 100% cotton. Perfect for business or casual wear.',
        short_description: 'Classic fit shirt made from premium cotton',
        sku: 'SHI-PRECOT-003',
        price: 65.99,
        sale_price: 45.99,
        category_id: 6,
        brand: 'Luxe Fashion',
        colors: JSON.stringify(['White', 'Blue', 'Black']),
        sizes: JSON.stringify(['S', 'M', 'L', 'XL', 'XXL']),
        stock_quantity: 75,
        is_featured: false,
        badge: 'Sale'
      },
      // Women's Jeans
      {
        name: 'Designer Skinny Jeans',
        slug: 'designer-skinny-jeans',
        description: 'High-waisted skinny jeans with stretch comfort and premium denim construction.',
        short_description: 'High-waisted skinny jeans with stretch comfort',
        sku: 'BOT-DESSKI-004',
        price: 129.99,
        sale_price: null,
        category_id: 3,
        brand: 'Luxe Fashion',
        colors: JSON.stringify(['Dark Blue', 'Black', 'Light Blue']),
        sizes: JSON.stringify(['24', '26', '28', '30', '32', '34']),
        stock_quantity: 40,
        is_featured: true,
        badge: 'Bestseller'
      },
      // Luxury Handbag
      {
        name: 'Luxury Leather Handbag',
        slug: 'luxury-leather-handbag',
        description: 'Premium leather handbag with gold hardware and spacious interior compartments.',
        short_description: 'Premium leather handbag with gold hardware',
        sku: 'BAG-LUXLEA-005',
        price: 199.99,
        sale_price: null,
        category_id: 5,
        brand: 'Luxe Fashion',
        colors: JSON.stringify(['Black', 'Brown', 'Burgundy']),
        sizes: JSON.stringify(['One Size']),
        stock_quantity: 25,
        is_featured: false,
        badge: 'Limited'
      }
    ];

    for (const product of products) {
      await executeQuery(
        `INSERT INTO products (
          name, slug, description, short_description, sku, price, sale_price,
          category_id, brand, colors, sizes, stock_quantity, is_featured, badge
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          product.name, product.slug, product.description, product.short_description,
          product.sku, product.price, product.sale_price, product.category_id,
          product.brand, product.colors, product.sizes, product.stock_quantity,
          product.is_featured, product.badge
        ]
      );
    }

    console.log('✅ Products seeded');

    // Seed Product Images
    const productImages = [
      { product_id: 1, image_url: '/images/products/dress1.jpg', alt_text: 'Elegant Summer Dress', is_primary: true },
      { product_id: 1, image_url: '/images/products/dress1-2.jpg', alt_text: 'Elegant Summer Dress Back View', is_primary: false },
      { product_id: 2, image_url: '/images/products/dress2.jpg', alt_text: 'Floral Maxi Dress', is_primary: true },
      { product_id: 3, image_url: '/images/products/shirt1.jpg', alt_text: 'Premium Cotton Shirt', is_primary: true },
      { product_id: 4, image_url: '/images/products/jeans1.jpg', alt_text: 'Designer Skinny Jeans', is_primary: true },
      { product_id: 5, image_url: '/images/products/bag1.jpg', alt_text: 'Luxury Leather Handbag', is_primary: true }
    ];

    for (const image of productImages) {
      await executeQuery(
        'INSERT INTO product_images (product_id, image_url, alt_text, is_primary) VALUES (?, ?, ?, ?)',
        [image.product_id, image.image_url, image.alt_text, image.is_primary]
      );
    }

    console.log('✅ Product images seeded');

    // Seed Reviews
    const reviews = [
      {
        product_id: 1,
        user_id: 1,
        rating: 5,
        title: 'Perfect summer dress!',
        comment: 'Absolutely love this dress! The fabric is so comfortable and the fit is perfect.',
        is_verified_purchase: true,
        is_approved: true
      },
      {
        product_id: 1,
        user_id: 2,
        rating: 4,
        title: 'Great quality',
        comment: 'Beautiful dress, great quality fabric. Runs a bit large so consider sizing down.',
        is_verified_purchase: true,
        is_approved: true
      },
      {
        product_id: 3,
        user_id: 1,
        rating: 5,
        title: 'Excellent shirt',
        comment: 'Great quality cotton shirt. Perfect for both work and casual occasions.',
        is_verified_purchase: true,
        is_approved: true
      }
    ];

    for (const review of reviews) {
      await executeQuery(
        'INSERT INTO reviews (product_id, user_id, rating, title, comment, is_verified_purchase, is_approved) VALUES (?, ?, ?, ?, ?, ?, ?)',
        [review.product_id, review.user_id, review.rating, review.title, review.comment, review.is_verified_purchase, review.is_approved]
      );
    }

    console.log('✅ Reviews seeded');

    // Seed Newsletter Subscriptions
    const newsletters = [
      { email: '<EMAIL>', first_name: 'Alice' },
      { email: '<EMAIL>', first_name: 'Bob' },
      { email: '<EMAIL>', first_name: 'Carol' }
    ];

    for (const newsletter of newsletters) {
      await executeQuery(
        'INSERT INTO newsletter_subscriptions (email, first_name, unsubscribe_token) VALUES (?, ?, ?)',
        [newsletter.email, newsletter.first_name, Math.random().toString(36).substring(2, 15)]
      );
    }

    console.log('✅ Newsletter subscriptions seeded');

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📋 Sample Data Summary:');
    console.log('- 13 Categories');
    console.log('- 3 Users (password: password123)');
    console.log('- 5 Products');
    console.log('- 6 Product Images');
    console.log('- 3 Reviews');
    console.log('- 3 Newsletter Subscriptions');
    console.log('\n👤 Sample User Accounts:');
    console.log('- <EMAIL> / password123');
    console.log('- <EMAIL> / password123');
    console.log('- <EMAIL> / password123');

  } catch (error) {
    console.error('❌ Seeding failed:', error);
    throw error;
  }
};

// Run seeding if this file is executed directly
if (require.main === module) {
  const { connectDB, closeConnection } = require('../config/database');
  
  const runSeeding = async () => {
    try {
      await connectDB();
      await seedData();
      await closeConnection();
      process.exit(0);
    } catch (error) {
      console.error('Seeding process failed:', error);
      process.exit(1);
    }
  };

  runSeeding();
}

module.exports = { seedData };
