const mysql = require('mysql2/promise');

let connection = null;

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'luxe_fashion',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  charset: 'utf8mb4',
  // Additional options for better connection handling
  multipleStatements: false,
  namedPlaceholders: true,
  dateStrings: false,
  debug: false,
  trace: true,
  ssl: false
};

const connectDB = async () => {
  try {
    // Create connection pool
    connection = mysql.createPool(dbConfig);

    // Test the connection
    const testConnection = await connection.getConnection();
    console.log(`📊 Connected to MySQL database: ${dbConfig.database}`);
    testConnection.release();

    return connection;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    throw error;
  }
};

const getConnection = () => {
  if (!connection) {
    throw new Error('Database not connected. Call connectDB() first.');
  }
  return connection;
};

const closeConnection = async () => {
  if (connection) {
    await connection.end();
    connection = null;
    console.log('📊 Database connection closed');
  }
};

// Helper function to execute queries
const executeQuery = async (query, params = []) => {
  try {
    const conn = getConnection();
    const [results] = await conn.execute(query, params);
    return results;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
};

// Helper function to execute transactions
const executeTransaction = async (queries) => {
  const conn = await getConnection().getConnection();

  try {
    await conn.beginTransaction();

    const results = [];
    for (const { query, params } of queries) {
      const [result] = await conn.execute(query, params);
      results.push(result);
    }

    await conn.commit();
    return results;
  } catch (error) {
    await conn.rollback();
    throw error;
  } finally {
    conn.release();
  }
};

module.exports = {
  connectDB,
  getConnection,
  closeConnection,
  executeQuery,
  executeTransaction
};
