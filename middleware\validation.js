const { body, param, query, validationResult } = require('express-validator');

// Handle validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  next();
};

// User validation rules
const validateRegister = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number'),
  body('first_name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('last_name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
  handleValidationErrors
];

const validateLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
  handleValidationErrors
];

// Product validation rules
const validateProduct = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('Product name must be between 2 and 255 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 5000 })
    .withMessage('Description must not exceed 5000 characters'),
  body('price')
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),
  body('category_id')
    .isInt({ min: 1 })
    .withMessage('Valid category ID is required'),
  body('stock_quantity')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Stock quantity must be a non-negative integer'),
  handleValidationErrors
];

// Cart validation rules
const validateCartItem = [
  body('product_id')
    .isInt({ min: 1 })
    .withMessage('Valid product ID is required'),
  body('quantity')
    .isInt({ min: 1, max: 99 })
    .withMessage('Quantity must be between 1 and 99'),
  body('selected_color')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Selected color must not exceed 50 characters'),
  body('selected_size')
    .optional()
    .trim()
    .isLength({ max: 20 })
    .withMessage('Selected size must not exceed 20 characters'),
  handleValidationErrors
];

// Order validation rules
const validateOrder = [
  body('shipping_address.first_name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('First name is required'),
  body('shipping_address.last_name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Last name is required'),
  body('shipping_address.address_line_1')
    .trim()
    .isLength({ min: 5, max: 255 })
    .withMessage('Address is required'),
  body('shipping_address.city')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('City is required'),
  body('shipping_address.state')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('State is required'),
  body('shipping_address.postal_code')
    .trim()
    .isLength({ min: 5, max: 20 })
    .withMessage('Postal code is required'),
  body('billing_address.first_name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Billing first name is required'),
  body('billing_address.last_name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Billing last name is required'),
  handleValidationErrors
];

// Newsletter validation rules
const validateNewsletter = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('first_name')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('First name must not exceed 100 characters'),
  handleValidationErrors
];

// Review validation rules
const validateReview = [
  body('rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('title')
    .optional()
    .trim()
    .isLength({ max: 255 })
    .withMessage('Title must not exceed 255 characters'),
  body('comment')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Comment must not exceed 2000 characters'),
  handleValidationErrors
];

// Parameter validation
const validateId = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Valid ID is required'),
  handleValidationErrors
];

// Query validation
const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  handleValidationErrors
];

module.exports = {
  validateRegister,
  validateLogin,
  validateProduct,
  validateCartItem,
  validateOrder,
  validateNewsletter,
  validateReview,
  validateId,
  validatePagination,
  handleValidationErrors
};
