const { executeQuery } = require('../config/database');
const { formatResponse } = require('../utils/helpers');

// @desc    Get user's wishlist
// @route   GET /api/wishlist
// @access  Private
const getWishlist = async (req, res, next) => {
  try {
    const userId = req.user.id;

    const wishlistQuery = `
      SELECT 
        w.id,
        w.created_at,
        p.id as product_id,
        p.name as product_name,
        p.slug as product_slug,
        p.price,
        p.sale_price,
        p.badge,
        p.stock_quantity,
        p.is_active,
        c.name as category_name,
        c.slug as category_slug,
        (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as product_image,
        (SELECT AVG(rating) FROM reviews WHERE product_id = p.id AND is_approved = 1) as avg_rating,
        (SELECT COUNT(*) FROM reviews WHERE product_id = p.id AND is_approved = 1) as review_count
      FROM wishlist w
      LEFT JOIN products p ON w.product_id = p.id
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE w.user_id = ?
      ORDER BY w.created_at DESC
    `;

    const wishlistItems = await executeQuery(wishlistQuery, [userId]);

    // Format wishlist items
    const formattedItems = wishlistItems.map(item => ({
      id: item.id,
      product_id: item.product_id,
      product_name: item.product_name,
      product_slug: item.product_slug,
      product_image: item.product_image,
      category_name: item.category_name,
      category_slug: item.category_slug,
      price: item.price,
      sale_price: item.sale_price,
      final_price: item.sale_price || item.price,
      discount_percentage: item.sale_price ? 
        Math.round(((item.price - item.sale_price) / item.price) * 100) : 0,
      badge: item.badge,
      stock_quantity: item.stock_quantity,
      is_available: item.is_active && item.stock_quantity > 0,
      avg_rating: item.avg_rating ? parseFloat(item.avg_rating).toFixed(1) : null,
      review_count: parseInt(item.review_count) || 0,
      added_at: item.created_at
    }));

    res.status(200).json(
      formatResponse(true, 'Wishlist retrieved successfully', {
        items: formattedItems,
        total_items: formattedItems.length
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Add item to wishlist
// @route   POST /api/wishlist
// @access  Private
const addToWishlist = async (req, res, next) => {
  try {
    const { product_id } = req.body;
    const userId = req.user.id;

    // Check if product exists
    const products = await executeQuery(
      'SELECT id, name FROM products WHERE id = ? AND is_active = 1',
      [product_id]
    );

    if (products.length === 0) {
      return res.status(404).json(
        formatResponse(false, 'Product not found')
      );
    }

    // Check if item already exists in wishlist
    const existingItems = await executeQuery(
      'SELECT id FROM wishlist WHERE user_id = ? AND product_id = ?',
      [userId, product_id]
    );

    if (existingItems.length > 0) {
      return res.status(400).json(
        formatResponse(false, 'Product already in wishlist')
      );
    }

    // Add to wishlist
    await executeQuery(
      'INSERT INTO wishlist (user_id, product_id) VALUES (?, ?)',
      [userId, product_id]
    );

    res.status(201).json(
      formatResponse(true, 'Product added to wishlist successfully')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Remove item from wishlist
// @route   DELETE /api/wishlist/:id
// @access  Private
const removeFromWishlist = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Remove from wishlist
    const result = await executeQuery(
      'DELETE FROM wishlist WHERE id = ? AND user_id = ?',
      [id, userId]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json(
        formatResponse(false, 'Wishlist item not found')
      );
    }

    res.status(200).json(
      formatResponse(true, 'Product removed from wishlist successfully')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Remove item from wishlist by product ID
// @route   DELETE /api/wishlist/product/:productId
// @access  Private
const removeFromWishlistByProduct = async (req, res, next) => {
  try {
    const { productId } = req.params;
    const userId = req.user.id;

    // Remove from wishlist
    const result = await executeQuery(
      'DELETE FROM wishlist WHERE product_id = ? AND user_id = ?',
      [productId, userId]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json(
        formatResponse(false, 'Product not found in wishlist')
      );
    }

    res.status(200).json(
      formatResponse(true, 'Product removed from wishlist successfully')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Clear entire wishlist
// @route   DELETE /api/wishlist
// @access  Private
const clearWishlist = async (req, res, next) => {
  try {
    const userId = req.user.id;

    await executeQuery(
      'DELETE FROM wishlist WHERE user_id = ?',
      [userId]
    );

    res.status(200).json(
      formatResponse(true, 'Wishlist cleared successfully')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Check if product is in wishlist
// @route   GET /api/wishlist/check/:productId
// @access  Private
const checkWishlistStatus = async (req, res, next) => {
  try {
    const { productId } = req.params;
    const userId = req.user.id;

    const existingItems = await executeQuery(
      'SELECT id FROM wishlist WHERE user_id = ? AND product_id = ?',
      [userId, productId]
    );

    res.status(200).json(
      formatResponse(true, 'Wishlist status checked', {
        in_wishlist: existingItems.length > 0,
        wishlist_id: existingItems.length > 0 ? existingItems[0].id : null
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Move wishlist item to cart
// @route   POST /api/wishlist/:id/move-to-cart
// @access  Private
const moveToCart = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { quantity = 1, selected_color, selected_size } = req.body;
    const userId = req.user.id;

    // Get wishlist item
    const wishlistItems = await executeQuery(
      `SELECT w.*, p.price, p.sale_price, p.stock_quantity, p.is_active 
       FROM wishlist w 
       LEFT JOIN products p ON w.product_id = p.id 
       WHERE w.id = ? AND w.user_id = ?`,
      [id, userId]
    );

    if (wishlistItems.length === 0) {
      return res.status(404).json(
        formatResponse(false, 'Wishlist item not found')
      );
    }

    const wishlistItem = wishlistItems[0];

    if (!wishlistItem.is_active) {
      return res.status(400).json(
        formatResponse(false, 'Product is not available')
      );
    }

    if (wishlistItem.stock_quantity < quantity) {
      return res.status(400).json(
        formatResponse(false, 'Insufficient stock available')
      );
    }

    // Check if item already exists in cart
    const existingCartItems = await executeQuery(
      'SELECT * FROM cart WHERE user_id = ? AND product_id = ? AND selected_color = ? AND selected_size = ?',
      [userId, wishlistItem.product_id, selected_color || null, selected_size || null]
    );

    const currentPrice = wishlistItem.sale_price || wishlistItem.price;

    if (existingCartItems.length > 0) {
      // Update existing cart item
      const existingItem = existingCartItems[0];
      const newQuantity = existingItem.quantity + quantity;

      if (wishlistItem.stock_quantity < newQuantity) {
        return res.status(400).json(
          formatResponse(false, 'Cannot add more items. Insufficient stock available')
        );
      }

      await executeQuery(
        'UPDATE cart SET quantity = ?, price = ? WHERE id = ?',
        [newQuantity, currentPrice, existingItem.id]
      );
    } else {
      // Add new item to cart
      await executeQuery(
        'INSERT INTO cart (user_id, product_id, quantity, selected_color, selected_size, price) VALUES (?, ?, ?, ?, ?, ?)',
        [userId, wishlistItem.product_id, quantity, selected_color || null, selected_size || null, currentPrice]
      );
    }

    // Remove from wishlist
    await executeQuery(
      'DELETE FROM wishlist WHERE id = ?',
      [id]
    );

    res.status(200).json(
      formatResponse(true, 'Product moved to cart successfully')
    );
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getWishlist,
  addToWishlist,
  removeFromWishlist,
  removeFromWishlistByProduct,
  clearWishlist,
  checkWishlistStatus,
  moveToCart
};
